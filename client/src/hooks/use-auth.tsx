import {
  createContext,
  ReactN<PERSON>,
  useContext,
  useState,
  useEffect,
} from "react";
import { useMutation, UseMutationResult } from "@tanstack/react-query";
import { insertUserSchema, User as SharedAppUser, InsertUser } from "@shared/schema";

// Custom AppUser type for Supabase authentication (uses string UUIDs instead of number IDs)
export type AppUser = Omit<SharedAppUser, 'id'> & {
  id: string; // Supabase uses string UUIDs
};
import { queryClient } from "../lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/lib/supabase";
import type { User as SupabaseUser, AuthError } from "@supabase/supabase-js";

// Supabase User type for authentication
type AuthUser = SupabaseUser;

// Simplified and consolidated auth context type
type AuthContextType = {
  // User access
  user: AppUser | null;
  isLoading: boolean;
  error: Error | null;

  // Use-case-specific methods
  loginMutation: UseMutationResult<AppUser, Error, LoginData>;
  logoutMutation: UseMutationResult<void, Error, void>;
  registerMutation: UseMutationResult<AppUser, Error, InsertUser>;
  googleLoginMutation: UseMutationResult<AppUser, Error, void>;

  // Legacy support (temporary)
  currentUser: AuthUser | null;
  loading: boolean;
};

type LoginData = {
  username: string;
  password: string;
};

// Single auth context
export const AuthContext = createContext<AuthContextType | null>(null);

// Utility function to transform Supabase user to app user
function createAppUserFromSupabase(supabaseUser: AuthUser): AppUser {
  return {
    id: supabaseUser.id, // Use the actual Supabase UUID string
    username:
      supabaseUser.user_metadata?.full_name ||
      supabaseUser.user_metadata?.name ||
      supabaseUser.email?.split("@")[0] ||
      "Usuario",
    email: supabaseUser.email || "",
    role: "user", // Default to user role
    isActive: true,
    createdAt: new Date(), // Add required field from shared schema
  };
}

export function AuthProvider({ children }: { children: ReactNode }) {
  const { toast } = useToast();
  const [currentUser, setCurrentUser] = useState<AuthUser | null>(null);
  const [appUser, setAppUser] = useState<AppUser | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Add a timeout to prevent infinite loading
  useEffect(() => {
    const timer = setTimeout(() => {
      if (isLoading) {
        console.log('⚠️ Auth: Loading timeout reached, forcing isLoading to false');
        setIsLoading(false);
      }
    }, 5000); // 5 second timeout

    return () => clearTimeout(timer);
  }, [isLoading]);
  const [error, setError] = useState<Error | null>(null);

  // Supabase auth state management
  useEffect(() => {
    console.log("🔄 Auth: Setting up Supabase auth state");

    // Add a race condition protection
    let mounted = true;

    // Get initial session with timeout
    const sessionPromise = supabase.auth.getSession();
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Session check timeout')), 3000);
    });

    Promise.race([sessionPromise, timeoutPromise])
      .then((result: any) => {
        if (!mounted) return;

        const { data: { session }, error } = result;

        if (error) {
          console.error("❌ Auth: Error getting session:", error);
          setError(new Error(error.message));
        }

        if (session?.user) {
          console.log("✅ Auth: Initial session found:", session.user.email);
          setCurrentUser(session.user);
          setAppUser(createAppUserFromSupabase(session.user));

          // If we're on the login page and have a session, redirect to dashboard
          if (window.location.pathname === '/login' || window.location.pathname === '/register') {
            console.log('🔄 Auth: User has session, redirecting from', window.location.pathname, 'to dashboard');
            setTimeout(() => {
              window.location.href = '/dashboard';
            }, 100);
          }
        } else {
          console.log("ℹ️ Auth: No initial session found");
        }

        console.log('🔄 Auth: Setting isLoading to false after session check');
        setIsLoading(false);
      })
      .catch((error) => {
        if (!mounted) return;

        console.error("❌ Auth: Session error:", error);
        setError(new Error(error.message));
        console.log('🔄 Auth: Setting isLoading to false after session error');
        setIsLoading(false);
      });

    return () => {
      mounted = false;
    };

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log("Auth state changed:", event, session?.user?.email);

      if (event === 'SIGNED_IN' && session?.user) {
        setCurrentUser(session.user);
        setAppUser(createAppUserFromSupabase(session.user));

        // Redirect to dashboard after successful OAuth
        if (window.location.pathname === '/login' || window.location.pathname === '/register' || window.location.pathname === '/') {
          setTimeout(() => {
            window.location.href = '/dashboard';
          }, 100);
        }
      } else if (event === 'SIGNED_OUT') {
        console.log("🚪 User signed out - clearing all state");

        // Clear user state
        setCurrentUser(null);
        setAppUser(null);

        // Clear all cached queries
        queryClient.clear();

        // Clear localStorage
        try {
          localStorage.removeItem('supabase.auth.token');
          localStorage.removeItem('sb-pthewpjbegkgomvyhkin-auth-token');

          // Clear any other Supabase related items
          Object.keys(localStorage).forEach(key => {
            if (key.startsWith('sb-') || key.includes('supabase')) {
              localStorage.removeItem(key);
            }
          });
        } catch (e) {
          console.warn("Could not clear auth tokens from localStorage:", e);
        }
      } else if (session?.user) {
        setCurrentUser(session.user);
        setAppUser(createAppUserFromSupabase(session.user));
      } else {
        setCurrentUser(null);
        setAppUser(null);
      }
      console.log('🔄 Auth: Setting isLoading to false after auth state change');
      setIsLoading(false);
    });

    return () => subscription.unsubscribe();
  }, []);

  // Login mutation with Supabase
  const loginMutation = useMutation({
    mutationFn: async (credentials: LoginData) => {
      try {
        console.log("Attempting Supabase login");

        const { data, error } = await supabase.auth.signInWithPassword({
          email: credentials.username,
          password: credentials.password,
        });

        if (error) {
          throw error;
        }

        if (!data.user) {
          throw new Error("No user returned from authentication");
        }

        return createAppUserFromSupabase(data.user);
      } catch (error: any) {
        console.error("Login error:", error);

        let errorMessage = "Authentication failed";

        // Extract meaningful error messages from Supabase errors
        if (error.message) {
          switch (error.message) {
            case "Invalid login credentials":
              errorMessage = "Invalid email or password";
              break;
            case "Email not confirmed":
              errorMessage = "Please check your email and confirm your account";
              break;
            case "Too many requests":
              errorMessage = "Too many failed login attempts. Please try again later";
              break;
            default:
              errorMessage = error.message || "Authentication error";
          }
        }

        throw new Error(errorMessage);
      }
    },
    onSuccess: (data) => {
      toast({
        title: "Login successful!",
        description: `Welcome back, ${data.username}`,
      });

      // Forzar navegación al dashboard después del login exitoso
      console.log(
        "Forzando navegación al dashboard desde loginMutation.onSuccess",
      );
      setTimeout(() => {
        window.location.href = "/dashboard";
      }, 500);
    },
    onError: (error: Error) => {
      toast({
        title: "Login error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Registration mutation with Supabase
  const registerMutation = useMutation({
    mutationFn: async (userData: InsertUser & { fullName?: string }) => {
      try {
        console.log("🔐 Attempting Supabase registration", {
          email: userData.email,
          username: userData.username,
          hasPassword: !!userData.password,
          fullName: userData.fullName
        });

        // Validate required fields
        if (!userData.email || !userData.password) {
          throw new Error("Email and password are required");
        }

        if (userData.password.length < 6) {
          throw new Error("Password should be at least 6 characters");
        }

        const { data, error } = await supabase.auth.signUp({
          email: userData.email,
          password: userData.password,
          options: {
            data: {
              full_name: userData.fullName || userData.username,
              username: userData.username,
            },
          },
        });

        if (error) {
          console.error("❌ Supabase registration error:", error);
          throw error;
        }

        if (!data.user) {
          throw new Error("No user returned from registration");
        }

        console.log("✅ Registration successful", { userId: data.user.id, email: data.user.email });
        return createAppUserFromSupabase(data.user);
      } catch (error: any) {
        console.error("❌ Registration error:", error);

        let errorMessage = "Registration failed";

        // Extract meaningful error messages from Supabase errors
        if (error.message) {
          switch (error.message) {
            case "User already registered":
              errorMessage = "This email is already registered";
              break;
            case "Password should be at least 6 characters":
              errorMessage = "Password is too weak. Please use a stronger password";
              break;
            case "Unable to validate email address: invalid format":
              errorMessage = "Invalid email format";
              break;
            case "Email and password are required":
              errorMessage = "Email and password are required";
              break;
            default:
              errorMessage = error.message || "Registration error";
          }
        }

        throw new Error(errorMessage);
      }
    },
    onSuccess: (user) => {
      console.log("🎉 Registration mutation onSuccess", { userId: user.id, email: user.email });

      toast({
        title: "¡Registro exitoso!",
        description: "Tu cuenta ha sido creada correctamente",
      });

      // Navigation will happen automatically via auth state change
      // But we can also force it as a fallback
      setTimeout(() => {
        if (window.location.pathname !== "/dashboard") {
          console.log("🔄 Forcing navigation to dashboard");
          window.location.href = "/dashboard";
        }
      }, 1000);
    },
    onError: (error: Error) => {
      console.error("❌ Registration mutation onError:", error);

      toast({
        title: "Error de registro",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Google login mutation with Supabase
  const googleLoginMutation = useMutation({
    mutationFn: async () => {
      try {
        console.log("Attempting Google login with Supabase");

        const { data, error } = await supabase.auth.signInWithOAuth({
          provider: 'google',
          options: {
            redirectTo: `${window.location.origin}/dashboard`,
            queryParams: {
              access_type: 'offline',
              prompt: 'consent',
            },
          },
        });

        if (error) {
          throw error;
        }

        // Note: For OAuth, the user will be redirected and the auth state will be handled by the auth listener
        // We return a placeholder user here, but the real user will be set by the auth state listener
        return {
          id: "loading",
          username: "Loading...",
          email: "<EMAIL>",
          role: "user",
          isActive: true,
          createdAt: new Date(),
        };
      } catch (error: any) {
        console.error("Google login error:", error);
        throw new Error(error.message || "Google authentication failed");
      }
    },
    onSuccess: () => {
      toast({
        title: "Redirecting...",
        description: "Redirecting to Google for authentication",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Google login error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Logout mutation with Supabase
  const logoutMutation = useMutation({
    mutationFn: async () => {
      console.log("🚪 Starting logout process with Supabase");

      try {
        // Sign out from Supabase with global scope to clear all sessions
        const { error } = await supabase.auth.signOut({
          scope: 'global'
        });

        if (error) {
          console.error("❌ Supabase signOut error:", error);
          throw error;
        }

        console.log("✅ Supabase signOut successful");

        // Force clear auth state immediately
        setCurrentUser(null);
        setAppUser(null);

        // Clear all cached queries
        queryClient.clear();

        // Clear localStorage auth tokens
        try {
          localStorage.removeItem('supabase.auth.token');
          localStorage.removeItem('sb-pthewpjbegkgomvyhkin-auth-token');

          // Clear any other Supabase related items
          Object.keys(localStorage).forEach(key => {
            if (key.startsWith('sb-') || key.includes('supabase')) {
              localStorage.removeItem(key);
            }
          });

          console.log("✅ localStorage cleared");
        } catch (e) {
          console.warn("Could not clear auth tokens from localStorage:", e);
        }

      } catch (error: any) {
        console.error("❌ Logout error:", error);

        // Force clear auth state even on error
        setCurrentUser(null);
        setAppUser(null);
        queryClient.clear();

        throw error;
      }
    },
    onSuccess: () => {
      console.log("✅ Logout mutation onSuccess");

      toast({
        title: "Sesión cerrada",
        description: "Has cerrado sesión exitosamente",
      });

      // Force redirect to login page
      setTimeout(() => {
        window.location.href = "/login";
      }, 100);
    },
    onError: (error: Error) => {
      console.error("❌ Logout mutation onError:", error);

      toast({
        title: "Error al cerrar sesión",
        description: error.message,
        variant: "destructive",
      });

      // Force clear auth state even on error
      setAppUser(null);
      setCurrentUser(null);
      queryClient.clear();

      // Even on error, redirect to login
      setTimeout(() => {
        window.location.href = "/login";
      }, 100);
    },
  });

  return (
    <AuthContext.Provider
      value={{
        // New API
        user: appUser,
        isLoading,
        error,
        loginMutation,
        logoutMutation,
        registerMutation,
        googleLoginMutation,

        // Legacy support
        currentUser,
        loading: isLoading,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
